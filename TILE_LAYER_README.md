# 地砖层功能说明

## 概述
本项目已添加了地砖层功能，可以在游戏背景和消除元素之间显示类似围棋棋盘的地砖效果。

## 功能特点
- 🎯 **精确对齐**: 地砖与游戏元素位置完美对齐，不会出现错位
- 🎨 **交替显示**: 采用围棋棋盘模式，交替显示两种不同的地砖纹理
- 📐 **自动缩放**: 地砖会根据网格大小自动调整缩放比例
- 🔄 **动态更新**: 支持游戏过程中动态修改地砖样式
- 📍 **层级管理**: 地砖层位于背景层(z_index=-1)和游戏元素层(z_index=1)之间(z_index=0)

## 文件结构
```
Scenes/
├── Game.tscn          # 主游戏场景，包含TileLayer节点
└── ...

Scripts/
├── Grid.gd            # 网格脚本，包含地砖层逻辑
└── ...

Assets/
├── fight_blockBG_1.png  # 地砖纹理1
├── fight_blockBG_2.png  # 地砖纹理2
└── ...
```

## 使用方法

### 1. 自动创建地砖
地砖层会在游戏启动时自动创建，无需手动操作。

### 2. 动态更新地砖（可选）
在游戏逻辑中，可以调用以下函数来动态更新特定位置的地砖：

```gdscript
# 更新指定位置的地砖为纹理1
update_tile_at_position(grid_x, grid_y, true)

# 更新指定位置的地砖为纹理2
update_tile_at_position(grid_x, grid_y, false)
```

## 技术实现

### 核心函数
- `setup_tile_layer()`: 创建地砖层，在游戏初始化时调用
- `update_tile_at_position(grid_x, grid_y, use_texture_1)`: 动态更新指定位置的地砖

### 坐标系统
- 地砖使用与游戏元素相同的坐标系统
- 通过 `grid_to_pixel()` 函数确保位置完全对齐
- 支持空白区域跳过（通过 `restricted_fill()` 检查）

### 纹理选择逻辑
```gdscript
# 围棋棋盘模式：根据坐标和决定纹理
if (i + j) % 2 == 0:
    # 使用纹理1
else:
    # 使用纹理2
```

## 自定义配置

### 修改地砖大小
地砖大小会根据 `offset` 参数自动调整。如需修改：
1. 在 `Game.tscn` 中调整 Grid 节点的 `offset` 属性
2. 地砖会自动重新缩放以匹配新的网格大小

### 更换地砖纹理
1. 将新的地砖图片放入 `Assets/` 目录
2. 更新 `Grid.gd` 中的纹理路径：
   ```gdscript
   var tile_texture_1 = preload("res://Assets/your_tile_1.png")
   var tile_texture_2 = preload("res://Assets/your_tile_2.png")
   ```

## 性能优化
- 地砖在游戏启动时一次性创建，避免运行时开销
- 使用预加载纹理，减少动态加载时间
- 支持按需更新，只修改需要变化的地砖

## 故障排除

### 地砖不显示
1. 检查 `Assets/` 目录是否包含地砖图片文件
2. 确认 `TileLayer` 节点存在于 `Game.tscn` 中
3. 检查 z_index 设置是否正确

### 地砖位置错位
1. 确认 Grid 节点的 `offset` 和坐标参数设置正确
2. 检查 `grid_to_pixel()` 函数的计算逻辑

### 性能问题
1. 避免频繁调用 `update_tile_at_position()`
2. 考虑批量更新多个地砖

---

🎉 现在你可以享受带有精美地砖层的三消游戏了！如有问题，请检查上述配置或查看代码注释。