[gd_scene load_steps=3 format=3 uid="uid://ynpouyser6kn"]

[ext_resource type="Script" uid="uid://ctby26erqrrgv" path="res://Scripts/Grid.gd" id="1"]
[ext_resource type="Texture2D" uid="uid://c0lpnicth1q3n" path="res://Assets/fight_BG.png" id="2"]

[node name="Game" type="Node"]

[node name="ColorRect" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.3)

[node name="TopContainer" type="Control" parent="."]
layout_mode = 3
anchor_right = 1.0
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2

[node name="TopBackground" type="ColorRect" parent="TopContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.6, 0.9, 0.8)

[node name="GridContainer" type="Control" parent="."]
clip_contents = true
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="GridBackgroundImage" type="Sprite2D" parent="GridContainer"]
z_index = -2
texture = ExtResource("2")

[node name="GridBackground" type="ColorRect" parent="GridContainer"]
z_index = -1
layout_mode = 0
offset_left = -55.0
offset_top = -55.0
offset_right = 825.0
offset_bottom = 715.0
color = Color(0.1, 0.3, 0.7, 0.1)

[node name="TileLayer" type="Node2D" parent="GridContainer"]
z_index = 10

[node name="Grid" type="Node2D" parent="GridContainer"]
script = ExtResource("1")
width = 8
height = 7
offset = 110
y_offset = -2
