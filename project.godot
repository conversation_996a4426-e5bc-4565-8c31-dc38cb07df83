; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Match3 Game Template"
run/main_scene="res://Scenes/Game.tscn"
config/features=PackedStringArray("4.4")
boot_splash/bg_color=Color(0, 0, 0, 1)
config/icon="res://icon.png"

[display]

window/size/viewport_width=900
window/size/viewport_height=1600
window/stretch/mode="2d"
window/handheld/orientation="portrait"

[input]

ui_touch={
"deadzone": 0.5,
"events": [Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"button_mask":0,"position":Vector2(0, 0),"global_position":Vector2(0, 0),"factor":1.0,"button_index":1,"canceled":false,"pressed":false,"double_click":false,"script":null)
]
}

[rendering]

environment/defaults/default_environment="res://default_env.tres"
