extends Node2D

@export var color = ""
@onready var sprite = get_node("Sprite2D")
var matched = false
var is_selected = false
var breathing_tween: Tween
var base_scale: float = 1.35  # 基础缩放值

func _ready():
	pass

# 更新基础缩放值（由Grid.gd调用）
func update_scale_values(new_scale: float):
	base_scale = new_scale
	
func move(target):
	var tween = get_tree().create_tween()
	tween.tween_property(self, 'position', target, 0.2)

func dim():
	sprite.modulate = Color(1, 1, 1, 0.5)

# 设置选中状态
func set_selected(selected: bool):
	# 如果已经是选中状态，不重复播放动画
	if is_selected == selected:
		return
		
	is_selected = selected
	if selected:
		start_breathing_effect()
	else:
		stop_breathing_effect()

# 开始呼吸效果动画
func start_breathing_effect():
	stop_breathing_effect()  # 先停止之前的动画
	breathing_tween = create_tween()
	breathing_tween.set_loops()  # 设置循环
	# 使用动态计算的基础尺寸
	var original_scale = Vector2(base_scale, base_scale)  # 使用动态基础尺寸
	var breathing_scale = Vector2(base_scale * 1.08, base_scale * 1.08)  # 比基础尺寸大8%的呼吸尺寸，保持无间隙
	# 创建呼吸效果：从原始大小到呼吸大小，再回到原始大小
	breathing_tween.tween_property(self, "scale", breathing_scale, 0.6)
	breathing_tween.tween_property(self, "scale", original_scale, 0.6)

# 停止呼吸效果动画
func stop_breathing_effect():
	if breathing_tween:
		breathing_tween.kill()
		breathing_tween = null
	# 恢复到动态计算的基础大小
	scale = Vector2(base_scale, base_scale)
