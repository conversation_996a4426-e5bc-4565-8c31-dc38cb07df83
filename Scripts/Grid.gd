extends Node2D

enum {wait, move}
var state

@export var width: int
@export var height: int
@export var offset: int
@export var y_offset: int

# 计算棋盘在屏幕下半部分的位置
@onready var x_start = ((get_window().size.x / 2.0) - ((width/2.0) * offset ) + (offset / 2))
@onready var y_start = (get_window().size.y * 0.75) + ((height/2.0) * offset ) - (offset / 2)

@export var empty_spaces: PackedVector2Array

@onready var possible_dots = [
	preload("res://Scenes/Dots/blue_dot.tscn"),
	preload("res://Scenes/Dots/green_dot.tscn"),
	preload("res://Scenes/Dots/pink_dot.tscn"),
	preload("res://Scenes/Dots/red_dot.tscn"),
]

var destroy_timer = Timer.new()
var collapse_timer = Timer.new()
var refill_timer = Timer.new()

var all_dots = []

var dot_one = null
var dot_two = null
var last_place = Vector2(0,0)
var last_direction = Vector2(0,0)
var move_checked = false


var first_touch = Vector2(0,0)
var final_touch = Vector2(0,0)
var controlling = false
var selected_dot = null  # 当前选中的元素
var dots_container: Node2D  # 专门管理所有消除元素的容器

func _ready():
	state = move
	setup_timers()
	randomize()
	all_dots = make_2d_array()
	setup_grid_background()
	setup_tile_layer()  # 在生成元素之前创建地砖层
	setup_dots_container()  # 创建消除元素容器
	spawn_dots()

# 设置网格背景容器和裁剪区域
func setup_grid_background():
	var background = get_parent().get_node("GridBackground")
	var background_image = get_parent().get_node("GridBackgroundImage")
	var container = get_parent()
	if background and background_image and container:
		# 计算网格的实际边界
		var grid_left = x_start - (offset / 2)
		var grid_top = y_start - (height - 1) * offset - (offset / 2)
		var grid_width = width * offset
		var grid_height = height * offset
		
		# 设置背景容器的位置和大小，添加一些边距
		var margin = 30
		
		# 设置容器的裁剪区域为网格区域
		container.position = Vector2(grid_left - margin, grid_top - margin)
		container.size = Vector2(grid_width + margin * 2, grid_height + margin * 2)
		
		# 调整Grid节点的位置，补偿容器位置的改变
		position = Vector2(-(grid_left - margin), -(grid_top - margin))
		
		# 设置背景图片的位置和缩放，让它填充整个网格区域
		setup_background_image(background_image, grid_width + margin * 2, grid_height + margin * 2)
		
		# 调整背景位置，因为容器位置改变了
		background.position = Vector2(0, 0)
		background.size = Vector2(grid_width + margin * 2, grid_height + margin * 2)

# 设置背景图片的位置和缩放
func setup_background_image(bg_image: Sprite2D, target_width: float, target_height: float):
	if bg_image and bg_image.texture:
		# 获取原始纹理尺寸
		var texture_size = bg_image.texture.get_size()
		
		# 计算缩放比例，保持宽高比
		var scale_x = target_width / texture_size.x
		var scale_y = target_height / texture_size.y
		var scale = min(scale_x, scale_y)  # 使用较小的缩放比例保持宽高比
		
		# 设置背景图片的位置和缩放
		bg_image.position = Vector2(target_width / 2, target_height / 2)
		bg_image.scale = Vector2(scale, scale)

# 设置地砖层，创建围棋棋盘样式的背景
func setup_tile_layer():
	# 加载地砖纹理（运行时加载，避免预加载错误）
	var tile_texture_1: Texture2D = load("res://Assets/fight_blockBG_1.png")
	var tile_texture_2: Texture2D = load("res://Assets/fight_blockBG_2.png")
	
	# 如果地砖纹理加载失败，使用默认纹理
	if not tile_texture_1:
		tile_texture_1 = preload("res://Assets/Dots/blue.png")
	if not tile_texture_2:
		tile_texture_2 = preload("res://Assets/Dots/red.png")
	
	# 网格坐标说明：
	# x轴：0是最左边，width-1是最右边
	# y轴：0是最上面，height-1是最下面
	# 围棋棋盘样式：(i+j)%2决定地砖样式
	
	# 为每个网格位置创建地砖
	for i in width:
		for j in height:
			# 跳过空白区域
			if restricted_fill(Vector2(i, j)):
				continue
			
			# 创建地砖精灵
			var tile_sprite = Sprite2D.new()
			
			# 根据棋盘模式选择纹理（围棋棋盘的交替模式）
			if (i + j) % 2 == 0:
				tile_sprite.texture = tile_texture_1
			else:
				tile_sprite.texture = tile_texture_2
			
			# 设置地砖位置，与游戏元素位置完全对齐
			tile_sprite.position = grid_to_pixel(i, j)
			
			# 设置地砖的z_index，在边框之上但在消除元素之下
			tile_sprite.z_index = 2
			
			# 根据offset大小调整地砖缩放，稍微缩小显示边框但保持无间隙  
			if tile_sprite.texture:
				var texture_size = tile_sprite.texture.get_size()
				var scale_factor = float(offset) / max(texture_size.x, texture_size.y) * 0.97  # 地砖再稍微放大一丝丝
				tile_sprite.scale = Vector2(scale_factor, scale_factor)
			
			# 直接添加到Grid节点
			add_child(tile_sprite)
	
	print("地砖层已创建，共创建了", width * height, "个地砖位置")
	print("围棋棋盘样式：两种纹理交替显示，无间隔填充")

# 创建专门管理所有消除元素的容器
func setup_dots_container():
	dots_container = Node2D.new()
	dots_container.name = "DotsContainer"
	dots_container.z_index = 5  # 确保消除元素容器在最上层
	
	# 计算边框的尺寸和位置，适度缩小但保持边框效果
	var border_width = 3  # 缩小边框宽度
	var margin_reduction = 4  # 适度缩小整体尺寸
	var grid_left = x_start - (offset / 2) - border_width + margin_reduction
	var grid_top = y_start - (height - 1) * offset - (offset / 2) - border_width + margin_reduction
	var grid_width = width * offset + border_width * 2 - margin_reduction * 2
	var grid_height = height * offset + border_width * 2 - margin_reduction * 2
	
	# 为容器添加边框（作为独立节点添加到Grid，在地砖层之下）
	var border_panel = Panel.new()
	border_panel.name = "BorderPanel"
	border_panel.size = Vector2(grid_width, grid_height)
	border_panel.position = Vector2(grid_left, grid_top)
	border_panel.z_index = 1  # 边框在地砖层之下，作为背景
	
	# 添加圆角样式
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color(0.278, 0.118, 0.114, 0.7)  # #471E1D颜色
	style_box.corner_radius_top_left = 10
	style_box.corner_radius_top_right = 10
	style_box.corner_radius_bottom_left = 10
	style_box.corner_radius_bottom_right = 10
	border_panel.add_theme_stylebox_override("panel", style_box)
	
	# 直接添加边框和消除元素容器到Grid
	add_child(border_panel)  # 边框作为独立节点
	add_child(dots_container)  # 消除元素容器在最上层
	print("消除元素容器已创建，边框尺寸已适度缩小，保持无间隔效果")

# 动态更新地砖样式（供游戏逻辑调用）
func update_tile_at_position(grid_x: int, grid_y: int, use_texture_1: bool = true):
	var tile_layer = get_parent().get_node("TileLayer")
	if not tile_layer:
		return
	
	# 获取纹理（运行时加载，避免预加载错误）
	var target_texture: Texture2D
	if use_texture_1:
		target_texture = load("res://Assets/fight_blockBG_1.png")
		if not target_texture:
			target_texture = preload("res://Assets/Dots/blue.png")
	else:
		target_texture = load("res://Assets/fight_blockBG_2.png")
		if not target_texture:
			target_texture = preload("res://Assets/Dots/red.png")
	
	# 查找对应位置的地砖
	var tile_index = grid_y * width + grid_x
	if tile_index < tile_layer.get_child_count():
		var tile_sprite = tile_layer.get_child(tile_index) as Sprite2D
		if tile_sprite:
			tile_sprite.texture = target_texture
	
func setup_timers():
	destroy_timer.connect("timeout", Callable(self, "destroy_matches"))
	destroy_timer.set_one_shot(true)
	destroy_timer.set_wait_time(0.2)
	add_child(destroy_timer)
	
	collapse_timer.connect("timeout", Callable(self, "collapse_columns"))
	collapse_timer.set_one_shot(true)
	collapse_timer.set_wait_time(0.2)
	add_child(collapse_timer)

	refill_timer.connect("timeout", Callable(self, "refill_columns"))
	refill_timer.set_one_shot(true)
	refill_timer.set_wait_time(0.2)
	add_child(refill_timer)
	
func restricted_fill(place):
	if is_in_array(empty_spaces, place):
		return true
	return false
	
func is_in_array(array, item):
	for i in array.size():
		if array[i] == item:
			return true
	return false

func make_2d_array():
	var array = []
	for i in width:
		array.append([])
		for j in height:
			array[i].append(null)
	return array

# 动态调整消除元素缩放，确保完全填充格子空间
func adjust_dot_scale(dot):
	var sprite = dot.get_node("Sprite2D")
	if sprite and sprite.texture:
		var texture_size = sprite.texture.get_size()
		# 计算需要的缩放比例，稍微缩小但仍确保无间隙
		var scale_factor = float(offset) / max(texture_size.x, texture_size.y) * 0.88  # 稍微缩小消除元素图标一丝丝
		dot.scale = Vector2(scale_factor, scale_factor)
		
		# 更新Dot脚本中的缩放值，确保呼吸效果和重置功能正常
		if dot.has_method("update_scale_values"):
			dot.update_scale_values(scale_factor)

func spawn_dots():
	for i in width:
		for j in height:
			if !restricted_fill(Vector2(i, j)):
				var rand = floor(randf_range(0, possible_dots.size()))
				var dot = possible_dots[rand].instantiate()
				var loops = 0
				while (match_at(i, j, dot.color) && loops < 100):
					rand = floor(randf_range(0,possible_dots.size()))
					loops += 1
					dot = possible_dots[rand].instantiate()
				# 将消除元素添加到专门的容器中
				dots_container.add_child(dot)
				dot.position = grid_to_pixel(i, j)
				
				# 动态计算消除元素的缩放，确保完全填充格子空间
				adjust_dot_scale(dot)
				
				all_dots[i][j] = dot
			
func match_at(i, j, color):
	if i > 1:
		if all_dots[i - 1][j] != null && all_dots[i - 2][j] != null:
			if all_dots[i - 1][j].color == color && all_dots[i - 2][j].color == color:
				return true
	if j > 1:
		if all_dots[i][j - 1] != null && all_dots[i][j - 2] != null:
			if all_dots[i][j - 1].color == color && all_dots[i][j - 2].color == color:
				return true
	pass

func grid_to_pixel(column, row):
	# 通过稍微缩小间距来消除元素间间隙，不改变元素尺寸
	var element_spacing = offset * 0.97  # 稍微增大间距到96%，保持适度重叠
	# 计算居中偏移，使消除元素网格整体居中
	var spacing_diff = offset - element_spacing  # 间距差值
	var center_offset_x = (width - 1) * spacing_diff / 2  # 水平居中偏移
	var center_offset_y = -(height - 1) * spacing_diff / 2  # 垂直居中偏移
	var new_x = x_start + element_spacing * column + center_offset_x
	var new_y = y_start + -element_spacing * row + center_offset_y
	return Vector2(new_x, new_y)
	
func pixel_to_grid(pixel_x,pixel_y):
	# 使用调整后的间距进行转换，与grid_to_pixel保持一致
	var element_spacing = offset * 0.97  # 与grid_to_pixel保持一致的间距
	# 计算居中偏移，与grid_to_pixel保持一致
	var spacing_diff = offset - element_spacing  # 间距差值
	var center_offset_x = (width - 1) * spacing_diff / 2  # 水平居中偏移
	var center_offset_y = -(height - 1) * spacing_diff / 2  # 垂直居中偏移
	var new_x = round((pixel_x - x_start - center_offset_x) / element_spacing)
	var new_y = round((pixel_y - y_start - center_offset_y) / -element_spacing)
	return Vector2(new_x, new_y)

func is_in_grid(grid_position):
	if grid_position.x >= 0 && grid_position.x < width:
		if grid_position.y >= 0 && grid_position.y < height:
			return true
	return false

func touch_input():
	if Input.is_action_just_pressed("ui_touch"):
		if is_in_grid(pixel_to_grid(get_global_mouse_position().x,get_global_mouse_position().y)):
			first_touch = pixel_to_grid(get_global_mouse_position().x,get_global_mouse_position().y)
			controlling = true
			# 处理元素选中逻辑
			handle_dot_selection(first_touch)
	if Input.is_action_just_released("ui_touch"):
		if is_in_grid(pixel_to_grid(get_global_mouse_position().x,get_global_mouse_position().y)) && controlling:
			controlling = false
			final_touch = pixel_to_grid(get_global_mouse_position().x,get_global_mouse_position().y )
			# 如果有选中的元素且拖拽到不同位置，进行交换
			if selected_dot != null && first_touch != final_touch:
				touch_difference(first_touch, final_touch)
				clear_selection()
			
func swap_dots(column, row, direction):
	var first_dot = all_dots[column][row]
	var other_dot = all_dots[column + direction.x][row + direction.y]
	if first_dot != null && other_dot != null:
		store_info(first_dot, other_dot, Vector2(column, row), direction)
		state = wait
		all_dots[column][row] = other_dot
		all_dots[column + direction.x][row + direction.y] = first_dot
		first_dot.move(grid_to_pixel(column + direction.x, row + direction.y))
		other_dot.move(grid_to_pixel(column, row))
		# 交换后清除选中状态
		clear_selection()
		if !move_checked:
			find_matches()
		
func store_info(first_dot, other_dot, place, direciton):
	dot_one = first_dot
	dot_two = other_dot
	last_place = place
	last_direction = direciton
	pass
		
func swap_back():
	if dot_one != null && dot_two != null:
		swap_dots(last_place.x, last_place.y, last_direction)
	state = move
	move_checked = false
	
# 处理元素选中逻辑
func handle_dot_selection(grid_pos: Vector2):
	var clicked_dot = all_dots[grid_pos.x][grid_pos.y]
	if clicked_dot == null:
		return
	
	# 如果点击的是已选中的元素，取消选中
	if selected_dot == clicked_dot:
		clear_selection()
		return
	
	# 如果已经有选中的元素，先清除之前的选中状态
	if selected_dot != null:
		selected_dot.set_selected(false)
	
	# 选中新元素
	selected_dot = clicked_dot
	selected_dot.set_selected(true)

# 清除选中状态
func clear_selection():
	if selected_dot != null:
		selected_dot.set_selected(false)
		selected_dot = null

func touch_difference(grid_1, grid_2):
	var difference = grid_2 - grid_1
	if abs(difference.x) > abs(difference.y):
		if difference.x > 0:
			swap_dots(grid_1.x, grid_1.y, Vector2(1, 0))
		elif difference.x < 0:
			swap_dots(grid_1.x, grid_1.y, Vector2(-1, 0))
	elif abs(difference.y) > abs(difference.x):
		if difference.y > 0:
			swap_dots(grid_1.x, grid_1.y, Vector2(0, 1))
		elif difference.y < 0:
			swap_dots(grid_1.x, grid_1.y, Vector2(0, -1))

func _process(_delta):
	if state == move:
		touch_input()
	
func find_matches():
	for i in width:
		for j in height:
			if all_dots[i][j] != null:
				var current_color = all_dots[i][j].color
				if i > 0 && i < width -1:
					if !is_piece_null(i - 1, j) && !is_piece_null(i + 1, j):
						if all_dots[i - 1][j].color == current_color && all_dots[i + 1][j].color == current_color:
							match_and_dim(all_dots[i - 1][j])
							match_and_dim(all_dots[i][j])
							match_and_dim(all_dots[i + 1][j])
				if j > 0 && j < height -1:
					if !is_piece_null(i, j - 1) && !is_piece_null(i, j + 1):
						if all_dots[i][j - 1].color == current_color && all_dots[i][j + 1].color == current_color:
							match_and_dim(all_dots[i][j - 1])
							match_and_dim(all_dots[i][j])
							match_and_dim(all_dots[i][j + 1])
	destroy_timer.start()

func is_piece_null(column, row):
	if all_dots[column][row] == null:
		return true
	return false

func match_and_dim(item):
	item.matched = true
	item.dim()

func destroy_matches():
	var was_matched = false
	for i in width:
		for j in height:
			if all_dots[i][j] != null:
				if all_dots[i][j].matched:
					was_matched = true
					all_dots[i][j].queue_free()
					all_dots[i][j] = null
	move_checked = true
	if was_matched:
		collapse_timer.start()
	else:
		swap_back()
					
func collapse_columns():
	for i in width:
		for j in height:
			if all_dots[i][j] == null && !restricted_fill(Vector2(i,j)):
				for k in range(j + 1, height):
					if all_dots[i][k] != null:
						all_dots[i][k].move(grid_to_pixel(i, j))
						all_dots[i][j] = all_dots[i][k]
						all_dots[i][k] = null
						break
	refill_timer.start()

func refill_columns():
	for i in width:
		for j in height:
			if all_dots[i][j] == null && !restricted_fill(Vector2(i,j)):
				var rand = floor(randf_range(0, possible_dots.size()))
				var dot = possible_dots[rand].instantiate()
				var loops = 0
				while (match_at(i, j, dot.color) && loops < 100):
					rand = floor(randf_range(0,possible_dots.size()))
					loops += 1
					dot = possible_dots[rand].instantiate()
				# 将新生成的消除元素添加到专门的容器中
				dots_container.add_child(dot)
				dot.position = grid_to_pixel(i, j - y_offset)
				
				# 动态计算消除元素的缩放，确保完全填充格子空间
				adjust_dot_scale(dot)
				
				dot.move(grid_to_pixel(i,j))
				all_dots[i][j] = dot
	after_refill()
				
func after_refill():
	for i in width:
		for j in height:
			if all_dots[i][j] != null:
				if match_at(i, j, all_dots[i][j].color):
					find_matches()
					destroy_timer.start()
					return
	state = move
	move_checked = false
